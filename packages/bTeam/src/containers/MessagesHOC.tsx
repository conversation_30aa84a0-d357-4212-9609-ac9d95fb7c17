import React, { useEffect, useCallback, useRef, useState, memo } from "react";
import { <PERSON>, BackHand<PERSON>, useWindowDimensions, FlatList } from "react-native";
import {
  useRoute,
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native";
import { useThemeAwareObject, Theme } from "b-ui-lib";
import { useDispatch, useSelector } from "react-redux";

// Components
import MessageH<PERSON> from "./MessageHOC";
import { performMarkAsReadUnreadAction } from "../slices/gridMessageSlice";

type Props = {};

// Individual message component - subscribes only to its own data
const MessageItem = memo(
  ({
    UMS_Guid,
    width,
    onRegisterVisibilityCallback,
  }: {
    UMS_Guid: string;
    width: number;
    onRegisterVisibilityCallback: (
      umsGuid: string,
      callback: (isVisible: boolean) => void
    ) => void;
  }) => {
    return (
      <View style={{ width }}>
        <MessageHOC
          UMS_Guid={UMS_Guid}
          onRegisterVisibilityCallback={onRegisterVisibilityCallback}
        />
      </View>
    );
  }
);

// Memoized FlatList - won't re-render when container updates
const MemoizedMessagesList = memo(
  ({
    emailIds,
    initialIndex,
    width,
    onViewableItemsChanged,
    onRegisterVisibilityCallback,
  }: {
    emailIds: string[];
    initialIndex: number;
    width: number;
    onViewableItemsChanged: (info: any) => void;
    onRegisterVisibilityCallback: (
      umsGuid: string,
      callback: (isVisible: boolean) => void
    ) => void;
  }) => {
    const { color } = useThemeAwareObject((theme: Theme) => ({
      color: theme.color,
    }));

    const renderMessageItem = useCallback(
      ({ item: UMS_Guid }: { item: string; index: number }) => {
        return (
          <MessageItem
            UMS_Guid={UMS_Guid}
            width={width}
            onRegisterVisibilityCallback={onRegisterVisibilityCallback}
          />
        );
      },
      [width, onRegisterVisibilityCallback]
    );

    const keyExtractor = useCallback((item: string) => item, []);

    const getItemLayout = useCallback(
      (_data: any, index: number) => ({
        length: width,
        offset: width * index,
        index,
      }),
      [width]
    );

    return (
      <FlatList
        style={{ flex: 1, backgroundColor: color.BACKGROUND }}
        data={emailIds}
        renderItem={renderMessageItem}
        keyExtractor={keyExtractor}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        initialScrollIndex={initialIndex}
        getItemLayout={getItemLayout}
        maintainVisibleContentPosition={{
          minIndexForVisible: 0,
        }}
        // Optimized for smooth swiping
        windowSize={5}
        maxToRenderPerBatch={3}
        updateCellsBatchingPeriod={300}
        removeClippedSubviews={false}
        initialNumToRender={5}
        onViewableItemsChanged={onViewableItemsChanged}
      />
    );
  }
);

// Container component
const MessagesHOC: React.FC<Props> = () => {
  const route = useRoute();
  const dispatch = useDispatch();
  const navigation = useNavigation<NavigationProp<ParamListBase>>();
  const { width } = useWindowDimensions();
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  const allMessages = useSelector(
    (state: any) => state.persist.gridMessageSlice.gridMessages
  );

  // Ref to store visibility callbacks for each message
  const visibilityCallbacksRef = useRef<
    Map<string, (isVisible: boolean) => void>
  >(new Map());
  const currentVisibleMessageRef = useRef<string | null>(null);

  // Get data from route params and Redux state
  const initialUMS_Guid = (route.params as any)?.UMS_Guid;
  const targetFolderId = (route.params as any)?.folderId;

  // Get emailIds from Redux state instead of route params
  const emailIds = useSelector(
    (state: any) =>
      state.persist.gridMessageSlice.folders.byId[targetFolderId]?.emailIds ||
      []
  );

  const messages = emailIds.map((id: string) => allMessages.byId[id]);

  const initialIndex = emailIds.findIndex(
    (id: string) => id === initialUMS_Guid
  );

  const handleBackPress = useCallback(() => {
    navigation.goBack();
    return true;
  }, [navigation]);

  // Register visibility callback for each MessageHOC
  const registerVisibilityCallback = useCallback(
    (umsGuid: string, callback: (isVisible: boolean) => void) => {
      visibilityCallbacksRef.current.set(umsGuid, callback);

      // If this is the initially visible message, call the callback immediately
      if (umsGuid === initialUMS_Guid) {
        currentVisibleMessageRef.current = umsGuid;
        callback(true);
      }

      // Cleanup function
      return () => {
        visibilityCallbacksRef.current.delete(umsGuid);
      };
    },
    [initialUMS_Guid]
  );

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", handleBackPress);

    // Cleanup debounce timer on unmount
    return () => {
      BackHandler.removeEventListener("hardwareBackPress", handleBackPress);
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [handleBackPress]);

  const _onViewableItemsChanged = useCallback((info: any) => {
    const { viewableItems } = info;

    if (viewableItems && viewableItems.length > 0) {
      // Get the currently visible message (assuming single message visible due to paging)
      const visibleItem = viewableItems[0];
      const visibleUmsGuid = visibleItem.item;

      // Only update if the visible message has changed
      if (currentVisibleMessageRef.current !== visibleUmsGuid) {
        // Mark previous message as not visible
        if (currentVisibleMessageRef.current) {
          const prevCallback = visibilityCallbacksRef.current.get(
            currentVisibleMessageRef.current
          );
          if (prevCallback) {
            prevCallback(false);
          }
        }

        // Mark new message as visible
        const newCallback = visibilityCallbacksRef.current.get(visibleUmsGuid);
        if (newCallback) {
          newCallback(true);
        }

        currentVisibleMessageRef.current = visibleUmsGuid;

        dispatch(
          performMarkAsReadUnreadAction({
            ids: [visibleUmsGuid],
            mode: 6,
          })
        );

        const isFlagged = messages.find(
          (m: any) => m.id === visibleUmsGuid
        )?.isFlagged;

        const isViewed = messages.find(
          (m: any) => m.id === visibleUmsGuid
        )?.isViewed;

        navigation.setOptions({
          isFlagged: isFlagged,
          isViewed: isViewed,
        });
      }
    }
  }, []);

  return (
    <View style={{ flex: 1 }}>
      <MemoizedMessagesList
        emailIds={emailIds}
        initialIndex={initialIndex}
        width={width}
        onViewableItemsChanged={_onViewableItemsChanged}
        onRegisterVisibilityCallback={registerVisibilityCallback}
      />
    </View>
  );
};

export default MessagesHOC;
